server:
  http:
    addr: 0.0.0.0:8030
    timeout: 10s
  grpc:
    addr: 0.0.0.0:8031
    timeout: 10s
data:
  database:
    driver: postgres
    #source: postgres://root:root@localhost:5432/annout?sslmode=disable
    endpoint: postgres
    port: 5432
    database: annout
    username: root
    password: root
    options: sslmode=disable
    max_idle_conns: 5
    max_open_conns: 20
    conn_max_idle_time: 600s
  redis:
    addr: redis:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

workspace:

temporal:
  disable_worker: false
  addr: temporal:7233
  namespace:
  task_queue: annout

ktwf:
  master:
    #image: artifactory.rp.konvery.work/docker/sansheng-annout:latest
    #command: ["/app/annout", "-conf", "/data/conf"]
    args: []
    #service_account: sansheng-annout
    # storage_class: # 留空，在Docker环境中不使用存储类
  worker:
    # namespace: # 留空，在Docker环境中不使用namespace
    # service_account: # 留空

rpc:
  # service account used for RPC calls
  svc_account: aaaaaaaanno

  iam:
    addr: iam:8001
  anno:
    addr: anno:8011
  annofeed:
    addr: annofeed:8021

otel:
  tracing:
    endpoint: #127.0.0.1:4317
  metrics:
    serve_addr: #:6060
  log:
    level: info
    format: default

object_storage:
  # storage type:
  # "s3": store the uploaded files into S3;
  # "localfs": use local filesystem; this is the default;
  type: s3

  # directory in local filesystem to put temporary files: e.g. downloaded files
  workdir:

  # local storage config
  localfs:
    # directory in local filesystem to put the uploaded files
    base_dir: upload

  s3:
    access_key:
    secret_key:

    # bucket for normal data
    bucket: non-prod-workload-sansheng
    # bucket for public resources: user/project/label avatars
    public_bucket: konvery-images-public-nonprod

    # default expires duration for signed URLs: 7 days (max is 7 days)
    expires: 604800s

  cloudfront:
    enabled: false
    # signer key ID
    sign_key_id:
    # signer private key (RSA) in PEM format
    sign_key:
    # default expires duration for signed URLs: 7 days (max is 7 days)
    expires: 604800s

    distributions:
      dist1:
        # matching origin, including the s3 scheme, bucket name and the path if any: s3://bucket/path
        origin: s3://konvery-images-public-nonprod
        # access URL prefix, including scheme and domain name: https://example.com
        url_prefix: https://s3npip.d.konvery.com
        # if it is a public distribution
        public: true
      dist2:
        origin: s3://non-prod-workload-sansheng
        url_prefix: https://s3npss.d.konvery.com
