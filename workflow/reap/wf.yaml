workflow:
  args:
    convDataImg: 'artifactory.rp.konvery.work/docker/python:3.11' # TODO: make it configurable
    convDataCmd: '["python", "main.py"]'
    data: "{}" # reaper

  # storage:
  #   calSizeActivity:
  #     activity: ReapGetVolumeSizeGB
  #     args: ["$<data>"]

  steps:
    - type: activity
      executor: WFMkdir
      args: ['{"/tmp/annout-workflow/data": 511}'] # use local tmp path for Docker environment
    - type: activity
      executor: ReapCollectAnnos
      args: ["/tmp/annout-workflow/data", "$<data>"]
      heartbeat: 60s
    # save original annos
    - type: activity
      executor: ReapSaveAnnos
      args: ["/tmp/annout-workflow/data", "$<data>"]

    # convert original annos if convert script is provided
    - type: activity
      executor: ReapConverterScript
      args: ["/tmp/annout-workflow/data", "$<data>"]
      resultRef: convScriptURI
    - type: activity
      executor: WFMkdir
      args: ['{"/tmp/annout-workflow/convert": 511}'] # use local tmp path for Docker environment
      when: 'convScriptURI != ""'
    # kubejob disabled for Docker environment - conversion will be skipped
    # - type: kubejob
    #   executor: convertData
    #   args: ["/tmp/annout-workflow/data", "/tmp/annout-workflow/convert"] # input, output
    #   when: 'convScriptURI != ""'
    - type: activity
      executor: ReapSaveConvertedAnnos
      args: ["/tmp/annout-workflow/convert", "$<data>"]
      when: 'convScriptURI != ""'
    - type: activity
      executor: ReapExportConvertedAnnos
      args: [ "/tmp/annout-workflow/convert", "$<data>" ]
      when: 'convScriptURI != ""'

kubeJobs:
  convertData:
    image: $<convDataImg> # docker image url and tag
    command: ["$<convDataCmd[*]>"]
    downloadFiles:
      "auto:main.py": "$<convScriptURI>"
